<?php

namespace app\controller\swagger;

use think\Response;
use OpenApi\Generator;

/**
 * Swagger文档控制器
 * 
 * @OA\Info(
 *     title="正式版视频平台API",
 *     version="1.0.0",
 *     description="正式版视频平台后端API接口文档",
 *     @OA\Contact(
 *         email="<EMAIL>",
 *         name="API支持团队"
 *     ),
 *     @OA\License(
 *         name="MIT",
 *         url="https://opensource.org/licenses/MIT"
 *     )
 * )
 * 
 * @OA\Server(
 *     url="http://localhost:8000",
 *     description="开发环境"
 * )
 * 
 * @OA\Server(
 *     url="https://api.example.com",
 *     description="生产环境"
 * )
 * 
 * @OA\SecurityScheme(
 *     securityScheme="bearerAuth",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT",
 *     description="JWT认证令牌"
 * )
 * 
 * @OA\Tag(
 *     name="认证",
 *     description="用户认证相关接口"
 * )
 * 
 * @OA\Tag(
 *     name="视频",
 *     description="视频管理相关接口"
 * )
 * 
 * @OA\Tag(
 *     name="用户",
 *     description="用户管理相关接口"
 * )
 * 
 * @OA\Tag(
 *     name="上传",
 *     description="文件上传相关接口"
 * )
 * 
 * @OA\Tag(
 *     name="分类",
 *     description="分类管理相关接口"
 * )
 * 
 * @OA\Tag(
 *     name="管理后台",
 *     description="管理后台相关接口"
 * )
 */
class SwaggerController
{
    /**
     * 生成Swagger JSON文档
     * 
     * @return Response
     */
    public function json(): Response
    {
        $openapi = Generator::scan([
            app_path(),
        ]);

        return response($openapi->toJson(), 200, [
            'Content-Type' => 'application/json',
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers' => 'Content-Type, Authorization'
        ]);
    }

    /**
     * Swagger UI界面
     * 
     * @return Response
     */
    public function ui(): Response
    {
        $html = '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>正式版视频平台 API 文档</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin:0;
            background: #fafafa;
        }
        .swagger-ui .topbar {
            background-color: #1976d2;
        }
        .swagger-ui .topbar .download-url-wrapper .select-label {
            color: #fff;
        }
        .swagger-ui .topbar .download-url-wrapper input[type=text] {
            border: 2px solid #1976d2;
        }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: "/swagger/json",
                dom_id: "#swagger-ui",
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                validatorUrl: null,
                docExpansion: "list",
                operationsSorter: "alpha",
                tagsSorter: "alpha",
                filter: true,
                tryItOutEnabled: true,
                requestInterceptor: function(request) {
                    // 自动添加认证头
                    const token = localStorage.getItem("swagger_token");
                    if (token) {
                        request.headers["Authorization"] = "Bearer " + token;
                    }
                    return request;
                },
                onComplete: function() {
                    // 添加认证功能
                    const authButton = document.createElement("button");
                    authButton.innerHTML = "设置认证令牌";
                    authButton.style.cssText = "position: fixed; top: 10px; right: 10px; z-index: 9999; padding: 8px 16px; background: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer;";
                    authButton.onclick = function() {
                        const token = prompt("请输入JWT令牌:");
                        if (token) {
                            localStorage.setItem("swagger_token", token);
                            alert("令牌已设置，刷新页面生效");
                        }
                    };
                    document.body.appendChild(authButton);
                }
            });
        };
    </script>
</body>
</html>';

        return response($html, 200, [
            'Content-Type' => 'text/html; charset=utf-8'
        ]);
    }

    /**
     * API文档首页
     * 
     * @return Response
     */
    public function index(): Response
    {
        $html = '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正式版视频平台 API 文档中心</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 60px;
        }
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            font-weight: 300;
        }
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        .cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }
        .card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        .card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }
        .card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #1976d2;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: background 0.3s ease;
        }
        .btn:hover {
            background: #1565c0;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }
        .stat {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            color: white;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>API 文档中心</h1>
            <p>正式版视频平台后端接口文档与开发指南</p>
        </div>
        
        <div class="cards">
            <div class="card">
                <h3>📚 Swagger API 文档</h3>
                <p>交互式API文档，支持在线测试接口，查看请求响应格式，是开发者的最佳伙伴。</p>
                <a href="/swagger/ui" class="btn">查看文档</a>
            </div>
            
            <div class="card">
                <h3>🚀 快速开始</h3>
                <p>新手开发者指南，包含环境搭建、认证流程、常用接口示例等内容。</p>
                <a href="/docs/quickstart" class="btn">开始使用</a>
            </div>
            
            <div class="card">
                <h3>🔧 SDK 下载</h3>
                <p>提供多种编程语言的SDK，让接口调用更加简单便捷。</p>
                <a href="/docs/sdk" class="btn">下载SDK</a>
            </div>
            
            <div class="card">
                <h3>💡 最佳实践</h3>
                <p>API使用最佳实践、性能优化建议、错误处理指南等高级内容。</p>
                <a href="/docs/best-practices" class="btn">查看指南</a>
            </div>
        </div>
        
        <div class="stats">
            <div class="stat">
                <div class="stat-number">50+</div>
                <div class="stat-label">API 接口</div>
            </div>
            <div class="stat">
                <div class="stat-number">99.9%</div>
                <div class="stat-label">服务可用性</div>
            </div>
            <div class="stat">
                <div class="stat-number">24/7</div>
                <div class="stat-label">技术支持</div>
            </div>
            <div class="stat">
                <div class="stat-number">1000+</div>
                <div class="stat-label">开发者</div>
            </div>
        </div>
    </div>
</body>
</html>';

        return response($html, 200, [
            'Content-Type' => 'text/html; charset=utf-8'
        ]);
    }
}
