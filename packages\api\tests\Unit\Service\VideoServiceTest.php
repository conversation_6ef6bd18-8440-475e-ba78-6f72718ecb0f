<?php

namespace tests\Unit\Service;

use PHPUnit\Framework\TestCase;
use app\service\VideoService;
use app\model\Video;
use Mockery;

/**
 * 视频服务单元测试
 * 
 * @covers \app\service\VideoService
 */
class VideoServiceTest extends TestCase
{
    private VideoService $videoService;
    private $mockVideoModel;

    protected function setUp(): void
    {
        parent::setUp();
        $this->videoService = new VideoService();
        $this->mockVideoModel = Mockery::mock('alias:' . Video::class);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 测试创建视频
     * 
     * @covers \app\service\VideoService::createVideo
     */
    public function testCreateVideo(): void
    {
        $videoData = [
            'title' => '测试视频',
            'description' => '这是一个测试视频',
            'video_type' => 'short',
            'category_id' => 1,
            'video_url' => '/uploads/videos/test.mp4',
            'cover_image' => '/uploads/covers/test.jpg'
        ];
        $userId = 1;

        // Mock Video模型的save方法
        $this->mockVideoModel
            ->shouldReceive('save')
            ->once()
            ->andReturn(true);

        $this->mockVideoModel
            ->shouldReceive('getData')
            ->once()
            ->andReturn(array_merge($videoData, ['id' => 1, 'user_id' => $userId]));

        $result = $this->videoService->createVideo($videoData, $userId);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('video_id', $result);
        $this->assertArrayHasKey('message', $result);
        $this->assertEquals('视频创建成功', $result['message']);
    }

    /**
     * 测试获取视频列表
     * 
     * @covers \app\service\VideoService::getVideoList
     */
    public function testGetVideoList(): void
    {
        $params = [
            'page' => 1,
            'limit' => 10,
            'video_type' => 'short',
            'status' => 'published'
        ];

        $mockVideos = [
            [
                'id' => 1,
                'title' => '测试视频1',
                'video_type' => 'short',
                'status' => 'published'
            ],
            [
                'id' => 2,
                'title' => '测试视频2',
                'video_type' => 'short',
                'status' => 'published'
            ]
        ];

        $this->mockVideoModel
            ->shouldReceive('where')
            ->andReturnSelf();

        $this->mockVideoModel
            ->shouldReceive('order')
            ->andReturnSelf();

        $this->mockVideoModel
            ->shouldReceive('paginate')
            ->once()
            ->andReturn([
                'data' => $mockVideos,
                'total' => 2,
                'per_page' => 10,
                'current_page' => 1
            ]);

        $result = $this->videoService->getVideoList($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertCount(2, $result['data']);
    }

    /**
     * 测试获取视频详情
     * 
     * @covers \app\service\VideoService::getVideoDetail
     */
    public function testGetVideoDetail(): void
    {
        $videoId = 1;
        $mockVideo = [
            'id' => 1,
            'title' => '测试视频',
            'description' => '这是一个测试视频',
            'video_type' => 'short',
            'status' => 'published',
            'view_count' => 100,
            'like_count' => 10
        ];

        $this->mockVideoModel
            ->shouldReceive('find')
            ->with($videoId)
            ->once()
            ->andReturn((object)$mockVideo);

        $result = $this->videoService->getVideoDetail($videoId);

        $this->assertIsArray($result);
        $this->assertEquals($mockVideo['id'], $result['id']);
        $this->assertEquals($mockVideo['title'], $result['title']);
    }

    /**
     * 测试更新视频
     * 
     * @covers \app\service\VideoService::updateVideo
     */
    public function testUpdateVideo(): void
    {
        $videoId = 1;
        $updateData = [
            'title' => '更新后的标题',
            'description' => '更新后的描述'
        ];

        $mockVideo = Mockery::mock();
        $mockVideo->shouldReceive('save')
            ->with($updateData)
            ->once()
            ->andReturn(true);

        $this->mockVideoModel
            ->shouldReceive('find')
            ->with($videoId)
            ->once()
            ->andReturn($mockVideo);

        $result = $this->videoService->updateVideo($videoId, $updateData);

        $this->assertTrue($result);
    }

    /**
     * 测试删除视频
     * 
     * @covers \app\service\VideoService::deleteVideo
     */
    public function testDeleteVideo(): void
    {
        $videoId = 1;

        $mockVideo = Mockery::mock();
        $mockVideo->shouldReceive('delete')
            ->once()
            ->andReturn(true);

        $this->mockVideoModel
            ->shouldReceive('find')
            ->with($videoId)
            ->once()
            ->andReturn($mockVideo);

        $result = $this->videoService->deleteVideo($videoId);

        $this->assertTrue($result);
    }

    /**
     * 测试批量发布视频
     * 
     * @covers \app\service\VideoService::batchPublishVideos
     */
    public function testBatchPublishVideos(): void
    {
        $videoIds = [1, 2, 3];

        $this->mockVideoModel
            ->shouldReceive('whereIn')
            ->with('id', $videoIds)
            ->once()
            ->andReturnSelf();

        $this->mockVideoModel
            ->shouldReceive('update')
            ->with(['status' => 'published', 'published_at' => Mockery::any()])
            ->once()
            ->andReturn(3);

        $result = $this->videoService->batchPublishVideos($videoIds);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('success_count', $result);
        $this->assertEquals(3, $result['success_count']);
    }

    /**
     * 测试增加视频观看次数
     * 
     * @covers \app\service\VideoService::incrementViewCount
     */
    public function testIncrementViewCount(): void
    {
        $videoId = 1;

        $this->mockVideoModel
            ->shouldReceive('where')
            ->with('id', $videoId)
            ->once()
            ->andReturnSelf();

        $this->mockVideoModel
            ->shouldReceive('inc')
            ->with('view_count')
            ->once()
            ->andReturn(true);

        $result = $this->videoService->incrementViewCount($videoId);

        $this->assertTrue($result);
    }

    /**
     * 测试搜索视频
     * 
     * @covers \app\service\VideoService::searchVideos
     */
    public function testSearchVideos(): void
    {
        $keyword = '测试';
        $params = [
            'page' => 1,
            'limit' => 10
        ];

        $mockResults = [
            [
                'id' => 1,
                'title' => '测试视频1',
                'description' => '包含测试关键词的视频'
            ]
        ];

        $this->mockVideoModel
            ->shouldReceive('where')
            ->andReturnSelf();

        $this->mockVideoModel
            ->shouldReceive('whereOr')
            ->andReturnSelf();

        $this->mockVideoModel
            ->shouldReceive('paginate')
            ->once()
            ->andReturn([
                'data' => $mockResults,
                'total' => 1
            ]);

        $result = $this->videoService->searchVideos($keyword, $params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertCount(1, $result['data']);
    }

    /**
     * 测试获取热门视频
     * 
     * @covers \app\service\VideoService::getHotVideos
     */
    public function testGetHotVideos(): void
    {
        $limit = 10;

        $mockHotVideos = [
            ['id' => 1, 'title' => '热门视频1', 'view_count' => 1000],
            ['id' => 2, 'title' => '热门视频2', 'view_count' => 800]
        ];

        $this->mockVideoModel
            ->shouldReceive('where')
            ->with('status', 'published')
            ->once()
            ->andReturnSelf();

        $this->mockVideoModel
            ->shouldReceive('order')
            ->with('view_count', 'desc')
            ->once()
            ->andReturnSelf();

        $this->mockVideoModel
            ->shouldReceive('limit')
            ->with($limit)
            ->once()
            ->andReturnSelf();

        $this->mockVideoModel
            ->shouldReceive('select')
            ->once()
            ->andReturn($mockHotVideos);

        $result = $this->videoService->getHotVideos($limit);

        $this->assertIsArray($result);
        $this->assertCount(2, $result);
    }
}
