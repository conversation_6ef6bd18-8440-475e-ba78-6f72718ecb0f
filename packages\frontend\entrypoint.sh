#!/bin/sh

echo "🚀 启动用户前端 (开发模式)"

# 等待API服务启动
echo "⏳ 等待API服务..."
while ! wget --spider --quiet --header="X-API-Key: ShiPinAdmin2024ProductionKey32Bytes!@#\$%^&*()_+" http://shipin-api/api/v1/health 2>/dev/null; do
    sleep 2
done
echo "✅ API服务连接成功"

# 函数：强制清理node_modules
cleanup_node_modules() {
    echo "🧹 强制清理node_modules..."
    # 使用更强力的清理方法
    find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
    rm -rf node_modules package-lock.json yarn.lock .npm 2>/dev/null || true
    # 清理npm缓存
    npm cache clean --force 2>/dev/null || true
}

# 函数：安装依赖
install_dependencies() {
    echo "📦 安装npm依赖..."

    # 设置npm配置
    npm config set fund false
    npm config set audit false
    npm config set progress false
    npm config set registry https://registry.npmmirror.com

    # 检测系统架构和平台
    ARCH=$(uname -m)
    PLATFORM=$(uname -s | tr '[:upper:]' '[:lower:]')
    echo "🔍 检测到系统: $PLATFORM-$ARCH"

    # 使用更简单的安装方法
    echo "📦 开始安装依赖..."
    if npm install --omit=optional --no-fund --no-audit --loglevel=error; then
        echo "✅ 依赖安装成功"

        # 检查并修复Rollup模块问题
        if ! node -e "require('rollup')" 2>/dev/null; then
            echo "🔧 修复Rollup模块..."
            npm install --force @rollup/rollup-linux-x64-musl 2>/dev/null || true
            npm install --force @rollup/rollup-linux-x64-gnu 2>/dev/null || true
        fi

        return 0
    fi

    echo "❌ npm安装失败，尝试使用yarn..."
    if command -v yarn >/dev/null 2>&1 || npm install -g yarn; then
        yarn install --non-interactive --silent
        return $?
    fi

    echo "❌ 所有安装方法都失败了"
    return 1
}

# 执行清理和安装
cleanup_node_modules
install_dependencies

# 验证vite是否可用
if ! npx vite --version >/dev/null 2>&1; then
    echo "❌ vite不可用，尝试全局安装..."
    npm install -g vite
fi

echo "✅ 用户前端启动完成 (开发模式)"
echo "🌐 访问地址: http://localhost:${FRONTEND_PORT:-3002}"

# 启动开发服务器
exec npm run dev
