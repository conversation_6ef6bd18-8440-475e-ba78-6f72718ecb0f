import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useUserStore } from '@/stores/user'

// Mock API
vi.mock('@/api', () => ({
  api: {
    post: vi.fn(),
    get: vi.fn(),
    put: vi.fn(),
    delete: vi.fn()
  }
}))

describe('User Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    // 清除localStorage mock
    vi.clearAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      const userStore = useUserStore()

      expect(userStore.user).toBeNull()
      expect(userStore.isLoggedIn).toBe(false)
      expect(userStore.isLoading).toBe(false)
      expect(userStore.error).toBeNull()
    })

    it('应该从localStorage读取token', () => {
      const mockToken = 'test-token-123'
      vi.mocked(localStorage.getItem).mockReturnValue(mockToken)

      const userStore = useUserStore()
      expect(userStore.token).toBe(mockToken)
    })
  })

  describe('计算属性', () => {
    it('isAuthenticated应该正确计算', () => {
      const userStore = useUserStore()

      // 初始状态应该是未认证
      expect(userStore.isAuthenticated).toBe(false)

      // 设置token和用户信息
      userStore.token = 'test-token'
      userStore.user = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        level: 1
      }

      expect(userStore.isAuthenticated).toBe(true)
    })
  })

  describe('登录功能', () => {
    it('应该能够成功登录', async () => {
      const userStore = useUserStore()
      const { api } = await import('@/api')

      const mockResponse = {
        success: true,
        data: {
          user: {
            id: 1,
            username: 'testuser',
            email: '<EMAIL>',
            level: 1
          },
          access_token: 'test-access-token',
          refresh_token: 'test-refresh-token'
        }
      }

      vi.mocked(api.post).mockResolvedValue(mockResponse)

      const loginData = {
        username: 'testuser',
        password: 'password123'
      }

      const result = await userStore.login(loginData)

      expect(result.success).toBe(true)
      expect(userStore.user).toEqual(mockResponse.data.user)
      expect(userStore.token).toBe(mockResponse.data.access_token)
      expect(userStore.isLoggedIn).toBe(true)
      expect(localStorage.setItem).toHaveBeenCalledWith('token', mockResponse.data.access_token)
    })

    it('应该处理登录失败', async () => {
      const userStore = useUserStore()
      const { api } = await import('@/api')

      const mockErrorResponse = {
        success: false,
        message: '用户名或密码错误'
      }

      vi.mocked(api.post).mockResolvedValue(mockErrorResponse)

      const loginData = {
        username: 'wronguser',
        password: 'wrongpassword'
      }

      const result = await userStore.login(loginData)

      expect(result.success).toBe(false)
      expect(result.message).toBe('用户名或密码错误')
      expect(userStore.user).toBeNull()
      expect(userStore.token).toBeNull()
      expect(userStore.isLoggedIn).toBe(false)
    })

    it('应该处理网络错误', async () => {
      const userStore = useUserStore()
      const { api } = await import('@/api')

      vi.mocked(api.post).mockRejectedValue(new Error('Network Error'))

      const loginData = {
        username: 'testuser',
        password: 'password123'
      }

      const result = await userStore.login(loginData)

      expect(result.success).toBe(false)
      expect(result.message).toContain('登录失败')
      expect(userStore.error).toBeTruthy()
    })
  })

  describe('注册功能', () => {
    it('应该能够成功注册', async () => {
      const userStore = useUserStore()
      const { api } = await import('@/api')

      const mockResponse = {
        success: true,
        data: {
          user: {
            id: 2,
            username: 'newuser',
            email: '<EMAIL>',
            level: 1
          },
          access_token: 'new-access-token'
        }
      }

      vi.mocked(api.post).mockResolvedValue(mockResponse)

      const registerData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        confirm_password: 'password123'
      }

      const result = await userStore.register(registerData)

      expect(result.success).toBe(true)
      expect(userStore.user).toEqual(mockResponse.data.user)
      expect(userStore.token).toBe(mockResponse.data.access_token)
    })

    it('应该处理注册失败', async () => {
      const userStore = useUserStore()
      const { api } = await import('@/api')

      const mockErrorResponse = {
        success: false,
        message: '用户名已存在'
      }

      vi.mocked(api.post).mockResolvedValue(mockErrorResponse)

      const registerData = {
        username: 'existinguser',
        email: '<EMAIL>',
        password: 'password123',
        confirm_password: 'password123'
      }

      const result = await userStore.register(registerData)

      expect(result.success).toBe(false)
      expect(result.message).toBe('用户名已存在')
    })
  })

  describe('登出功能', () => {
    it('应该能够成功登出', async () => {
      const userStore = useUserStore()
      const { api } = await import('@/api')

      // 先设置登录状态
      userStore.user = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        level: 1
      }
      userStore.token = 'test-token'
      userStore.isLoggedIn = true

      vi.mocked(api.post).mockResolvedValue({ success: true })

      await userStore.logout()

      expect(userStore.user).toBeNull()
      expect(userStore.token).toBeNull()
      expect(userStore.isLoggedIn).toBe(false)
      expect(localStorage.removeItem).toHaveBeenCalledWith('token')
    })
  })

  describe('获取用户信息', () => {
    it('应该能够获取用户信息', async () => {
      const userStore = useUserStore()
      const { api } = await import('@/api')

      const mockUserInfo = {
        success: true,
        data: {
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          level: 1,
          avatar: '/uploads/avatars/default.jpg'
        }
      }

      vi.mocked(api.get).mockResolvedValue(mockUserInfo)

      const result = await userStore.fetchUserInfo()

      expect(result.success).toBe(true)
      expect(userStore.user).toEqual(mockUserInfo.data)
    })

    it('应该处理获取用户信息失败', async () => {
      const userStore = useUserStore()
      const { api } = await import('@/api')

      vi.mocked(api.get).mockRejectedValue(new Error('Unauthorized'))

      const result = await userStore.fetchUserInfo()

      expect(result.success).toBe(false)
      expect(userStore.error).toBeTruthy()
    })
  })

  describe('更新用户信息', () => {
    it('应该能够更新用户信息', async () => {
      const userStore = useUserStore()
      const { api } = await import('@/api')

      // 设置初始用户信息
      userStore.user = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        level: 1
      }

      const updateData = {
        username: 'updateduser',
        email: '<EMAIL>'
      }

      const mockResponse = {
        success: true,
        data: {
          ...userStore.user,
          ...updateData
        }
      }

      vi.mocked(api.put).mockResolvedValue(mockResponse)

      const result = await userStore.updateProfile(updateData)

      expect(result.success).toBe(true)
      expect(userStore.user?.username).toBe(updateData.username)
      expect(userStore.user?.email).toBe(updateData.email)
    })
  })

  describe('初始化认证状态', () => {
    it('应该能够初始化认证状态', async () => {
      const userStore = useUserStore()
      const { api } = await import('@/api')

      const mockToken = 'stored-token'
      vi.mocked(localStorage.getItem).mockReturnValue(mockToken)

      const mockUserInfo = {
        success: true,
        data: {
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          level: 1
        }
      }

      vi.mocked(api.get).mockResolvedValue(mockUserInfo)

      await userStore.initializeAuth()

      expect(userStore.token).toBe(mockToken)
      expect(userStore.user).toEqual(mockUserInfo.data)
      expect(userStore.isLoggedIn).toBe(true)
    })

    it('应该处理无效token', async () => {
      const userStore = useUserStore()
      const { api } = await import('@/api')

      const mockToken = 'invalid-token'
      vi.mocked(localStorage.getItem).mockReturnValue(mockToken)

      vi.mocked(api.get).mockRejectedValue(new Error('Unauthorized'))

      await userStore.initializeAuth()

      expect(userStore.token).toBeNull()
      expect(userStore.user).toBeNull()
      expect(userStore.isLoggedIn).toBe(false)
      expect(localStorage.removeItem).toHaveBeenCalledWith('token')
    })
  })
})
