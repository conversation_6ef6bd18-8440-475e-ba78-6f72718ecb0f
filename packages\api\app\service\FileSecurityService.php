<?php

namespace app\service;

use think\facade\Log;
use think\facade\Config;
use think\file\UploadedFile;

/**
 * 文件安全服务
 * 
 * 功能：
 * 1. 文件类型验证
 * 2. 文件内容检查
 * 3. 恶意文件检测
 * 4. 文件名安全处理
 * 5. 病毒扫描（可选）
 * 6. 文件隔离和清理
 */
class FileSecurityService
{
    /**
     * 危险文件扩展名
     */
    private array $dangerousExtensions = [
        'php', 'php3', 'php4', 'php5', 'phtml', 'pht',
        'jsp', 'asp', 'aspx', 'cer', 'asa', 'cdx',
        'exe', 'com', 'bat', 'cmd', 'scr', 'pif',
        'vbs', 'vbe', 'js', 'jse', 'ws', 'wsf',
        'sh', 'bash', 'zsh', 'csh', 'pl', 'py',
        'rb', 'go', 'jar', 'war', 'ear'
    ];

    /**
     * 危险MIME类型
     */
    private array $dangerousMimeTypes = [
        'application/x-php',
        'application/x-httpd-php',
        'application/php',
        'text/php',
        'text/x-php',
        'application/x-executable',
        'application/x-msdownload',
        'application/x-msdos-program',
        'application/x-msi',
        'application/x-winexe'
    ];

    /**
     * 恶意文件签名
     */
    private array $maliciousSignatures = [
        // PHP后门特征
        'eval(',
        'base64_decode(',
        'gzinflate(',
        'str_rot13(',
        'system(',
        'exec(',
        'shell_exec(',
        'passthru(',
        'file_get_contents(',
        'file_put_contents(',
        'fopen(',
        'fwrite(',
        'include(',
        'require(',
        'include_once(',
        'require_once(',
        
        // JavaScript恶意代码
        '<script',
        'javascript:',
        'vbscript:',
        'onload=',
        'onerror=',
        'onclick=',
        
        // SQL注入特征
        'union select',
        'drop table',
        'delete from',
        'insert into',
        'update set',
        
        // 其他恶意特征
        '<?php',
        '<%',
        'FSO.CreateTextFile',
        'WScript.Shell',
        'document.cookie'
    ];

    /**
     * 验证上传文件安全性
     * 
     * @param UploadedFile $file 上传的文件
     * @param string $type 文件类型 (image/video/document)
     * @return array 验证结果
     */
    public function validateFile(UploadedFile $file, string $type = 'image'): array
    {
        $result = [
            'valid' => true,
            'errors' => [],
            'warnings' => [],
            'file_info' => []
        ];

        try {
            // 基础信息收集
            $originalName = $file->getOriginalName();
            $extension = strtolower($file->extension());
            $mimeType = $file->getMimeType();
            $size = $file->getSize();
            $tempPath = $file->getPathname();

            $result['file_info'] = [
                'original_name' => $originalName,
                'extension' => $extension,
                'mime_type' => $mimeType,
                'size' => $size,
                'temp_path' => $tempPath
            ];

            // 1. 检查文件扩展名
            if (!$this->validateExtension($extension, $type)) {
                $result['valid'] = false;
                $result['errors'][] = "不允许的文件扩展名: {$extension}";
            }

            // 2. 检查MIME类型
            if (!$this->validateMimeType($mimeType, $type)) {
                $result['valid'] = false;
                $result['errors'][] = "不允许的MIME类型: {$mimeType}";
            }

            // 3. 检查文件大小
            if (!$this->validateFileSize($size, $type)) {
                $result['valid'] = false;
                $result['errors'][] = "文件大小超过限制";
            }

            // 4. 检查危险扩展名
            if ($this->isDangerousExtension($extension)) {
                $result['valid'] = false;
                $result['errors'][] = "危险的文件类型";
            }

            // 5. 检查危险MIME类型
            if ($this->isDangerousMimeType($mimeType)) {
                $result['valid'] = false;
                $result['errors'][] = "危险的MIME类型";
            }

            // 6. 检查文件内容
            $contentCheck = $this->checkFileContent($tempPath);
            if (!$contentCheck['safe']) {
                $result['valid'] = false;
                $result['errors'] = array_merge($result['errors'], $contentCheck['threats']);
            }

            // 7. 检查文件名安全性
            $nameCheck = $this->validateFileName($originalName);
            if (!$nameCheck['safe']) {
                $result['warnings'][] = "文件名包含不安全字符，将被重命名";
            }

            // 8. 病毒扫描（如果启用）
            if (Config::get('security.upload.content_check.scan_virus', false)) {
                $virusCheck = $this->scanVirus($tempPath);
                if (!$virusCheck['clean']) {
                    $result['valid'] = false;
                    $result['errors'][] = "文件包含病毒或恶意代码";
                }
            }

            // 记录安全检查日志
            $this->logSecurityCheck($result, $file);

        } catch (\Exception $e) {
            $result['valid'] = false;
            $result['errors'][] = "文件安全检查失败: " . $e->getMessage();
            
            Log::error('File security check failed', [
                'file' => $originalName ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $result;
    }

    /**
     * 验证文件扩展名
     * 
     * @param string $extension 文件扩展名
     * @param string $type 文件类型
     * @return bool
     */
    private function validateExtension(string $extension, string $type): bool
    {
        $allowedTypes = Config::get('security.upload.allowed_types', []);
        
        if (!isset($allowedTypes[$type])) {
            return false;
        }

        return in_array($extension, $allowedTypes[$type]);
    }

    /**
     * 验证MIME类型
     * 
     * @param string $mimeType MIME类型
     * @param string $type 文件类型
     * @return bool
     */
    private function validateMimeType(string $mimeType, string $type): bool
    {
        $allowedMimes = [
            'image' => [
                'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'
            ],
            'video' => [
                'video/mp4', 'video/avi', 'video/quicktime', 'video/x-msvideo',
                'video/x-flv', 'video/webm'
            ],
            'audio' => [
                'audio/mpeg', 'audio/wav', 'audio/aac', 'audio/ogg'
            ],
            'document' => [
                'application/pdf', 'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'text/plain'
            ]
        ];

        if (!isset($allowedMimes[$type])) {
            return false;
        }

        return in_array($mimeType, $allowedMimes[$type]);
    }

    /**
     * 验证文件大小
     * 
     * @param int $size 文件大小
     * @param string $type 文件类型
     * @return bool
     */
    private function validateFileSize(int $size, string $type): bool
    {
        $maxSizes = Config::get('security.upload.max_size', []);
        
        if (!isset($maxSizes[$type])) {
            return false;
        }

        return $size <= $maxSizes[$type];
    }

    /**
     * 检查是否为危险扩展名
     * 
     * @param string $extension 文件扩展名
     * @return bool
     */
    private function isDangerousExtension(string $extension): bool
    {
        return in_array(strtolower($extension), $this->dangerousExtensions);
    }

    /**
     * 检查是否为危险MIME类型
     * 
     * @param string $mimeType MIME类型
     * @return bool
     */
    private function isDangerousMimeType(string $mimeType): bool
    {
        return in_array(strtolower($mimeType), $this->dangerousMimeTypes);
    }

    /**
     * 检查文件内容
     * 
     * @param string $filePath 文件路径
     * @return array
     */
    private function checkFileContent(string $filePath): array
    {
        $result = [
            'safe' => true,
            'threats' => []
        ];

        try {
            // 读取文件内容（限制读取大小以避免内存问题）
            $maxReadSize = 1024 * 1024; // 1MB
            $content = file_get_contents($filePath, false, null, 0, $maxReadSize);
            
            if ($content === false) {
                $result['threats'][] = "无法读取文件内容";
                $result['safe'] = false;
                return $result;
            }

            // 检查恶意签名
            foreach ($this->maliciousSignatures as $signature) {
                if (stripos($content, $signature) !== false) {
                    $result['safe'] = false;
                    $result['threats'][] = "检测到恶意代码特征: {$signature}";
                }
            }

            // 检查二进制文件头
            $header = substr($content, 0, 16);
            if ($this->checkBinaryHeader($header)) {
                $result['threats'][] = "检测到可疑的二进制文件头";
            }

            // 检查是否包含NULL字节
            if (strpos($content, "\0") !== false) {
                $result['safe'] = false;
                $result['threats'][] = "文件包含NULL字节，可能是恶意文件";
            }

        } catch (\Exception $e) {
            $result['safe'] = false;
            $result['threats'][] = "文件内容检查失败: " . $e->getMessage();
        }

        return $result;
    }

    /**
     * 检查二进制文件头
     * 
     * @param string $header 文件头
     * @return bool
     */
    private function checkBinaryHeader(string $header): bool
    {
        // 检查常见的可执行文件头
        $executableHeaders = [
            "\x4D\x5A",         // PE executable (Windows)
            "\x7F\x45\x4C\x46", // ELF executable (Linux)
            "\xCA\xFE\xBA\xBE", // Java class file
            "\xFE\xED\xFA\xCE", // Mach-O executable (macOS)
        ];

        foreach ($executableHeaders as $execHeader) {
            if (strpos($header, $execHeader) === 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 验证文件名安全性
     * 
     * @param string $filename 文件名
     * @return array
     */
    private function validateFileName(string $filename): array
    {
        $result = [
            'safe' => true,
            'issues' => []
        ];

        // 检查文件名长度
        if (strlen($filename) > 255) {
            $result['safe'] = false;
            $result['issues'][] = "文件名过长";
        }

        // 检查危险字符
        $dangerousChars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/', "\0"];
        foreach ($dangerousChars as $char) {
            if (strpos($filename, $char) !== false) {
                $result['safe'] = false;
                $result['issues'][] = "文件名包含危险字符: {$char}";
            }
        }

        // 检查路径遍历
        if (strpos($filename, '..') !== false) {
            $result['safe'] = false;
            $result['issues'][] = "文件名包含路径遍历字符";
        }

        // 检查隐藏文件
        if (strpos($filename, '.') === 0) {
            $result['issues'][] = "隐藏文件";
        }

        return $result;
    }

    /**
     * 病毒扫描
     * 
     * @param string $filePath 文件路径
     * @return array
     */
    private function scanVirus(string $filePath): array
    {
        $result = [
            'clean' => true,
            'threats' => []
        ];

        // 这里可以集成第三方病毒扫描引擎
        // 例如：ClamAV、VirusTotal API等
        
        // 示例：使用ClamAV命令行工具
        if (function_exists('exec') && Config::get('security.upload.content_check.clamav_enabled', false)) {
            $command = "clamscan --no-summary " . escapeshellarg($filePath);
            $output = [];
            $returnCode = 0;
            
            exec($command, $output, $returnCode);
            
            if ($returnCode !== 0) {
                $result['clean'] = false;
                $result['threats'][] = "病毒扫描检测到威胁";
            }
        }

        return $result;
    }

    /**
     * 生成安全的文件名
     * 
     * @param string $originalName 原始文件名
     * @param string $type 文件类型
     * @return string
     */
    public function generateSecureFilename(string $originalName, string $type = 'image'): string
    {
        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
        
        // 生成随机文件名
        $randomName = date('Y/m/d/') . uniqid() . '_' . mt_rand(1000, 9999);
        
        return $randomName . '.' . $extension;
    }

    /**
     * 隔离可疑文件
     * 
     * @param string $filePath 文件路径
     * @param array $threats 威胁信息
     * @return bool
     */
    public function quarantineFile(string $filePath, array $threats): bool
    {
        try {
            $quarantinePath = Config::get('security.upload.paths.quarantine', 'uploads/quarantine');
            
            if (!is_dir($quarantinePath)) {
                mkdir($quarantinePath, 0755, true);
            }
            
            $quarantineFile = $quarantinePath . '/' . basename($filePath) . '_' . time();
            
            if (move_uploaded_file($filePath, $quarantineFile)) {
                // 记录隔离日志
                Log::warning('File quarantined due to security threats', [
                    'original_path' => $filePath,
                    'quarantine_path' => $quarantineFile,
                    'threats' => $threats,
                    'timestamp' => time()
                ]);
                
                return true;
            }
        } catch (\Exception $e) {
            Log::error('Failed to quarantine file', [
                'file_path' => $filePath,
                'error' => $e->getMessage()
            ]);
        }
        
        return false;
    }

    /**
     * 记录安全检查日志
     * 
     * @param array $result 检查结果
     * @param UploadedFile $file 文件对象
     */
    private function logSecurityCheck(array $result, UploadedFile $file): void
    {
        $logData = [
            'file_name' => $file->getOriginalName(),
            'file_size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'extension' => $file->extension(),
            'valid' => $result['valid'],
            'errors' => $result['errors'],
            'warnings' => $result['warnings'],
            'ip' => request()->ip(),
            'user_agent' => request()->header('User-Agent'),
            'timestamp' => time()
        ];

        if (!$result['valid']) {
            Log::warning('File security check failed', $logData);
        } else {
            Log::info('File security check passed', $logData);
        }
    }
}
