<?php

return [
    // 监控开关
    'enabled' => env('MONITORING_ENABLED', true),

    // Elasticsearch配置
    'elasticsearch' => [
        'enabled' => env('ELASTICSEARCH_ENABLED', false),
        'hosts' => [
            env('ELASTICSEARCH_HOST', 'localhost:9200')
        ],
        'username' => env('ELASTICSEARCH_USERNAME', ''),
        'password' => env('ELASTICSEARCH_PASSWORD', ''),
        'index_prefix' => env('ELASTICSEARCH_INDEX_PREFIX', 'app-metrics'),
    ],

    // 性能阈值配置
    'thresholds' => [
        // 响应时间阈值（毫秒）
        'response_time' => env('MONITORING_RESPONSE_TIME_THRESHOLD', 2000),
        
        // 内存使用阈值（字节）
        'memory_usage' => env('MONITORING_MEMORY_THRESHOLD', 50 * 1024 * 1024), // 50MB
        
        // 数据库查询数量阈值
        'db_queries' => env('MONITORING_DB_QUERIES_THRESHOLD', 50),
        
        // 慢查询阈值（秒）
        'slow_query_threshold' => env('MONITORING_SLOW_QUERY_THRESHOLD', 1.0),
        
        // 错误率阈值（百分比）
        'error_rate' => env('MONITORING_ERROR_RATE_THRESHOLD', 5.0),
        
        // CPU负载阈值
        'cpu_load' => env('MONITORING_CPU_LOAD_THRESHOLD', 0.8),
        
        // 内存使用率阈值（百分比）
        'memory_usage_percent' => env('MONITORING_MEMORY_USAGE_PERCENT_THRESHOLD', 80),
    ],

    // 告警配置
    'alerts' => [
        // 告警开关
        'enabled' => env('MONITORING_ALERTS_ENABLED', true),
        
        // 告警通道
        'channels' => [
            'log' => [
                'enabled' => true,
                'level' => 'warning'
            ],
            'email' => [
                'enabled' => env('ALERT_EMAIL_ENABLED', false),
                'to' => env('ALERT_EMAIL_TO', '<EMAIL>'),
                'subject_prefix' => '[监控告警]'
            ],
            'webhook' => [
                'enabled' => env('ALERT_WEBHOOK_ENABLED', false),
                'url' => env('ALERT_WEBHOOK_URL', ''),
                'secret' => env('ALERT_WEBHOOK_SECRET', ''),
            ],
            'dingtalk' => [
                'enabled' => env('ALERT_DINGTALK_ENABLED', false),
                'webhook_url' => env('ALERT_DINGTALK_WEBHOOK', ''),
                'secret' => env('ALERT_DINGTALK_SECRET', ''),
            ]
        ],
        
        // 告警频率限制（秒）
        'rate_limit' => [
            'slow_response' => 300,      // 5分钟
            'high_memory' => 600,        // 10分钟
            'too_many_queries' => 300,   // 5分钟
            'application_error' => 3600, // 1小时
            'system_error' => 1800,      // 30分钟
        ]
    ],

    // 数据保留策略
    'retention' => [
        // 实时监控数据保留时间（秒）
        'realtime_metrics' => 3600, // 1小时
        
        // 历史数据保留天数
        'historical_data' => 30,
        
        // 错误日志保留天数
        'error_logs' => 90,
        
        // 性能日志保留天数
        'performance_logs' => 7,
    ],

    // 采样配置
    'sampling' => [
        // 是否启用采样
        'enabled' => env('MONITORING_SAMPLING_ENABLED', false),
        
        // 采样率（0-100）
        'rate' => env('MONITORING_SAMPLING_RATE', 10),
        
        // 总是采样的端点
        'always_sample' => [
            '/api/v1/auth/login',
            '/api/v1/auth/register',
            '/api/v1/videos/upload',
        ],
        
        // 从不采样的端点
        'never_sample' => [
            '/api/v1/health',
            '/api/v1/ping',
        ]
    ],

    // 监控指标配置
    'metrics' => [
        // 应用指标
        'application' => [
            'request_count' => true,
            'response_time' => true,
            'error_rate' => true,
            'throughput' => true,
        ],
        
        // 数据库指标
        'database' => [
            'query_count' => true,
            'query_time' => true,
            'slow_queries' => true,
            'connection_count' => true,
        ],
        
        // 缓存指标
        'cache' => [
            'hit_rate' => true,
            'miss_rate' => true,
            'operation_time' => true,
        ],
        
        // 系统指标
        'system' => [
            'cpu_usage' => true,
            'memory_usage' => true,
            'disk_usage' => true,
            'network_io' => true,
        ]
    ],

    // 自定义标签
    'tags' => [
        'application' => env('APP_NAME', 'zhengshiban'),
        'environment' => env('APP_ENV', 'production'),
        'version' => env('APP_VERSION', '1.0.0'),
        'server' => gethostname(),
    ],

    // 监控端点配置
    'endpoints' => [
        // 健康检查端点
        'health_check' => '/api/v1/health',
        
        // 监控数据端点
        'metrics' => '/api/v1/monitoring/metrics',
        
        // 实时监控端点
        'realtime' => '/api/v1/monitoring/realtime',
    ],

    // 性能分析配置
    'profiling' => [
        // 是否启用性能分析
        'enabled' => env('PROFILING_ENABLED', false),
        
        // 分析采样率
        'sample_rate' => env('PROFILING_SAMPLE_RATE', 1),
        
        // 分析数据保留时间（小时）
        'retention_hours' => 24,
        
        // 分析报告生成
        'reports' => [
            'daily' => true,
            'weekly' => true,
            'monthly' => false,
        ]
    ],

    // 业务指标配置
    'business_metrics' => [
        // 用户相关指标
        'user' => [
            'registration_count' => true,
            'login_count' => true,
            'active_users' => true,
        ],
        
        // 视频相关指标
        'video' => [
            'upload_count' => true,
            'view_count' => true,
            'like_count' => true,
            'comment_count' => true,
        ],
        
        // 系统相关指标
        'system' => [
            'api_calls' => true,
            'error_count' => true,
            'success_rate' => true,
        ]
    ],

    // 监控仪表板配置
    'dashboard' => [
        // 刷新间隔（秒）
        'refresh_interval' => 30,
        
        // 显示的时间范围选项
        'time_ranges' => [
            '5m' => '最近5分钟',
            '15m' => '最近15分钟',
            '1h' => '最近1小时',
            '6h' => '最近6小时',
            '24h' => '最近24小时',
            '7d' => '最近7天',
        ],
        
        // 默认时间范围
        'default_time_range' => '1h',
        
        // 图表配置
        'charts' => [
            'response_time' => [
                'type' => 'line',
                'title' => '响应时间',
                'unit' => 'ms'
            ],
            'request_count' => [
                'type' => 'bar',
                'title' => '请求数量',
                'unit' => 'count'
            ],
            'error_rate' => [
                'type' => 'line',
                'title' => '错误率',
                'unit' => '%'
            ],
            'memory_usage' => [
                'type' => 'area',
                'title' => '内存使用',
                'unit' => 'MB'
            ]
        ]
    ]
];
