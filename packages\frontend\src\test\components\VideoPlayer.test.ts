import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import VideoPlayer from '@/components/VideoPlayer.vue'

// Mock video.js
vi.mock('video.js', () => ({
  default: vi.fn(() => ({
    ready: vi.fn((callback) => callback()),
    play: vi.fn(),
    pause: vi.fn(),
    dispose: vi.fn(),
    currentTime: vi.fn(),
    duration: vi.fn(() => 100),
    volume: vi.fn(),
    muted: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
    trigger: vi.fn()
  }))
}))

describe('VideoPlayer组件', () => {
  let wrapper: any

  beforeEach(() => {
    vi.clearAllMocks()
  })

  const defaultProps = {
    src: 'https://example.com/video.mp4',
    poster: 'https://example.com/poster.jpg',
    autoplay: false,
    controls: true,
    width: 800,
    height: 450
  }

  describe('组件渲染', () => {
    it('应该正确渲染视频播放器', () => {
      wrapper = mount(VideoPlayer, {
        props: defaultProps
      })

      expect(wrapper.find('video').exists()).toBe(true)
      expect(wrapper.find('.video-player-container').exists()).toBe(true)
    })

    it('应该设置正确的视频属性', () => {
      wrapper = mount(VideoPlayer, {
        props: defaultProps
      })

      const video = wrapper.find('video')
      expect(video.attributes('poster')).toBe(defaultProps.poster)
      expect(video.attributes('width')).toBe(defaultProps.width.toString())
      expect(video.attributes('height')).toBe(defaultProps.height.toString())
    })

    it('应该根据props设置controls属性', () => {
      wrapper = mount(VideoPlayer, {
        props: {
          ...defaultProps,
          controls: true
        }
      })

      expect(wrapper.find('video').attributes('controls')).toBeDefined()

      wrapper = mount(VideoPlayer, {
        props: {
          ...defaultProps,
          controls: false
        }
      })

      expect(wrapper.find('video').attributes('controls')).toBeUndefined()
    })
  })

  describe('播放控制', () => {
    beforeEach(() => {
      wrapper = mount(VideoPlayer, {
        props: defaultProps
      })
    })

    it('应该能够播放视频', async () => {
      const videojs = await import('video.js')
      const mockPlayer = vi.mocked(videojs.default).mock.results[0].value

      await wrapper.vm.play()

      expect(mockPlayer.play).toHaveBeenCalled()
    })

    it('应该能够暂停视频', async () => {
      const videojs = await import('video.js')
      const mockPlayer = vi.mocked(videojs.default).mock.results[0].value

      await wrapper.vm.pause()

      expect(mockPlayer.pause).toHaveBeenCalled()
    })

    it('应该能够设置播放时间', async () => {
      const videojs = await import('video.js')
      const mockPlayer = vi.mocked(videojs.default).mock.results[0].value

      const time = 30
      await wrapper.vm.setCurrentTime(time)

      expect(mockPlayer.currentTime).toHaveBeenCalledWith(time)
    })

    it('应该能够获取视频时长', async () => {
      const videojs = await import('video.js')
      const mockPlayer = vi.mocked(videojs.default).mock.results[0].value

      const duration = wrapper.vm.getDuration()

      expect(mockPlayer.duration).toHaveBeenCalled()
      expect(duration).toBe(100)
    })
  })

  describe('音量控制', () => {
    beforeEach(() => {
      wrapper = mount(VideoPlayer, {
        props: defaultProps
      })
    })

    it('应该能够设置音量', async () => {
      const videojs = await import('video.js')
      const mockPlayer = vi.mocked(videojs.default).mock.results[0].value

      const volume = 0.5
      await wrapper.vm.setVolume(volume)

      expect(mockPlayer.volume).toHaveBeenCalledWith(volume)
    })

    it('应该能够静音/取消静音', async () => {
      const videojs = await import('video.js')
      const mockPlayer = vi.mocked(videojs.default).mock.results[0].value

      await wrapper.vm.mute()
      expect(mockPlayer.muted).toHaveBeenCalledWith(true)

      await wrapper.vm.unmute()
      expect(mockPlayer.muted).toHaveBeenCalledWith(false)
    })
  })

  describe('事件处理', () => {
    beforeEach(() => {
      wrapper = mount(VideoPlayer, {
        props: defaultProps
      })
    })

    it('应该监听播放事件', async () => {
      const videojs = await import('video.js')
      const mockPlayer = vi.mocked(videojs.default).mock.results[0].value

      // 模拟播放事件
      const playCallback = vi.mocked(mockPlayer.on).mock.calls.find(
        call => call[0] === 'play'
      )?.[1]

      if (playCallback) {
        playCallback()
      }

      expect(wrapper.emitted('play')).toBeTruthy()
    })

    it('应该监听暂停事件', async () => {
      const videojs = await import('video.js')
      const mockPlayer = vi.mocked(videojs.default).mock.results[0].value

      const pauseCallback = vi.mocked(mockPlayer.on).mock.calls.find(
        call => call[0] === 'pause'
      )?.[1]

      if (pauseCallback) {
        pauseCallback()
      }

      expect(wrapper.emitted('pause')).toBeTruthy()
    })

    it('应该监听时间更新事件', async () => {
      const videojs = await import('video.js')
      const mockPlayer = vi.mocked(videojs.default).mock.results[0].value

      const timeUpdateCallback = vi.mocked(mockPlayer.on).mock.calls.find(
        call => call[0] === 'timeupdate'
      )?.[1]

      if (timeUpdateCallback) {
        timeUpdateCallback()
      }

      expect(wrapper.emitted('timeupdate')).toBeTruthy()
    })

    it('应该监听播放结束事件', async () => {
      const videojs = await import('video.js')
      const mockPlayer = vi.mocked(videojs.default).mock.results[0].value

      const endedCallback = vi.mocked(mockPlayer.on).mock.calls.find(
        call => call[0] === 'ended'
      )?.[1]

      if (endedCallback) {
        endedCallback()
      }

      expect(wrapper.emitted('ended')).toBeTruthy()
    })
  })

  describe('响应式设计', () => {
    it('应该支持响应式尺寸', () => {
      wrapper = mount(VideoPlayer, {
        props: {
          ...defaultProps,
          responsive: true
        }
      })

      expect(wrapper.find('.video-player-container').classes()).toContain('responsive')
    })

    it('应该支持自定义宽高比', () => {
      const aspectRatio = '4:3'
      wrapper = mount(VideoPlayer, {
        props: {
          ...defaultProps,
          aspectRatio
        }
      })

      const container = wrapper.find('.video-player-container')
      expect(container.attributes('style')).toContain('aspect-ratio')
    })
  })

  describe('HLS支持', () => {
    it('应该支持HLS流', () => {
      const hlsSrc = 'https://example.com/playlist.m3u8'
      wrapper = mount(VideoPlayer, {
        props: {
          ...defaultProps,
          src: hlsSrc,
          type: 'application/x-mpegURL'
        }
      })

      expect(wrapper.vm.isHLS).toBe(true)
    })
  })

  describe('错误处理', () => {
    it('应该处理视频加载错误', async () => {
      wrapper = mount(VideoPlayer, {
        props: defaultProps
      })

      const videojs = await import('video.js')
      const mockPlayer = vi.mocked(videojs.default).mock.results[0].value

      const errorCallback = vi.mocked(mockPlayer.on).mock.calls.find(
        call => call[0] === 'error'
      )?.[1]

      if (errorCallback) {
        errorCallback()
      }

      expect(wrapper.emitted('error')).toBeTruthy()
    })

    it('应该显示错误信息', async () => {
      wrapper = mount(VideoPlayer, {
        props: {
          ...defaultProps,
          showError: true
        }
      })

      await wrapper.setData({ hasError: true, errorMessage: '视频加载失败' })

      expect(wrapper.find('.error-message').exists()).toBe(true)
      expect(wrapper.find('.error-message').text()).toBe('视频加载失败')
    })
  })

  describe('组件销毁', () => {
    it('应该在组件销毁时清理播放器', async () => {
      wrapper = mount(VideoPlayer, {
        props: defaultProps
      })

      const videojs = await import('video.js')
      const mockPlayer = vi.mocked(videojs.default).mock.results[0].value

      wrapper.unmount()

      expect(mockPlayer.dispose).toHaveBeenCalled()
    })
  })

  describe('自定义配置', () => {
    it('应该支持自定义播放器配置', () => {
      const customOptions = {
        fluid: true,
        playbackRates: [0.5, 1, 1.25, 1.5, 2],
        plugins: {
          hotkeys: {
            volumeStep: 0.1,
            seekStep: 5
          }
        }
      }

      wrapper = mount(VideoPlayer, {
        props: {
          ...defaultProps,
          options: customOptions
        }
      })

      const videojs = require('video.js')
      const initCall = vi.mocked(videojs.default).mock.calls[0]
      const passedOptions = initCall[1]

      expect(passedOptions.fluid).toBe(true)
      expect(passedOptions.playbackRates).toEqual(customOptions.playbackRates)
    })
  })
})
