<?php

namespace tests\Integration;

use PHPUnit\Framework\TestCase;
use think\facade\Db;
use app\model\User;
use app\model\Video;
use app\service\JwtService;

/**
 * 视频API集成测试
 * 
 * 测试视频相关API的完整流程
 */
class VideoApiTest extends TestCase
{
    private $testUser;
    private $testToken;
    private JwtService $jwtService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 初始化测试数据库
        $this->initTestDatabase();
        
        // 创建测试用户
        $this->createTestUser();
        
        // 初始化JWT服务
        $this->jwtService = new JwtService();
    }

    protected function tearDown(): void
    {
        // 清理测试数据
        $this->cleanupTestData();
        parent::tearDown();
    }

    /**
     * 初始化测试数据库
     */
    private function initTestDatabase(): void
    {
        // 确保使用测试数据库
        if (env('APP_ENV') !== 'testing') {
            $this->markTestSkipped('集成测试只能在测试环境中运行');
        }

        // 清空相关表
        Db::execute('TRUNCATE TABLE videos');
        Db::execute('TRUNCATE TABLE users');
        Db::execute('TRUNCATE TABLE video_categories');
    }

    /**
     * 创建测试用户
     */
    private function createTestUser(): void
    {
        $this->testUser = User::create([
            'username' => 'testuser_' . time(),
            'email' => 'test_' . time() . '@example.com',
            'password' => password_hash('password123', PASSWORD_ARGON2ID),
            'status' => 'active',
            'level' => 1
        ]);

        // 生成测试令牌
        $userData = [
            'id' => $this->testUser->id,
            'username' => $this->testUser->username,
            'email' => $this->testUser->email,
            'level' => $this->testUser->level
        ];
        
        $this->testToken = $this->jwtService->generateAccessToken($userData);
    }

    /**
     * 清理测试数据
     */
    private function cleanupTestData(): void
    {
        if ($this->testUser) {
            // 删除测试用户创建的视频
            Video::where('user_id', $this->testUser->id)->delete();
            
            // 删除测试用户
            $this->testUser->delete();
        }
    }

    /**
     * 发送API请求的辅助方法
     */
    private function apiRequest(string $method, string $url, array $data = [], array $headers = []): array
    {
        // 添加认证头
        if ($this->testToken) {
            $headers['Authorization'] = 'Bearer ' . $this->testToken;
        }

        // 这里应该使用实际的HTTP客户端发送请求
        // 为了演示，我们模拟API响应
        return $this->simulateApiRequest($method, $url, $data, $headers);
    }

    /**
     * 模拟API请求（实际项目中应该使用真实的HTTP客户端）
     */
    private function simulateApiRequest(string $method, string $url, array $data, array $headers): array
    {
        // 这里应该实现真实的API调用逻辑
        // 返回模拟响应
        return [
            'success' => true,
            'message' => 'API调用成功',
            'data' => []
        ];
    }

    /**
     * 测试视频创建完整流程
     */
    public function testVideoCreationFlow(): void
    {
        // 1. 创建视频分类
        $categoryData = [
            'name' => '测试分类',
            'type' => 'short',
            'status' => 'active'
        ];

        $categoryResponse = $this->apiRequest('POST', '/api/v1/admin/categories', $categoryData);
        $this->assertTrue($categoryResponse['success']);

        // 2. 上传视频文件
        $uploadData = [
            'file' => 'test_video.mp4',
            'type' => 'video'
        ];

        $uploadResponse = $this->apiRequest('POST', '/api/v1/upload/video', $uploadData);
        $this->assertTrue($uploadResponse['success']);

        // 3. 创建视频记录
        $videoData = [
            'title' => '集成测试视频',
            'description' => '这是一个集成测试创建的视频',
            'video_type' => 'short',
            'category_id' => 1,
            'video_url' => '/uploads/videos/test_video.mp4',
            'cover_image' => '/uploads/covers/test_cover.jpg',
            'tags' => ['测试', '集成测试'],
            'status' => 'draft'
        ];

        $createResponse = $this->apiRequest('POST', '/api/v1/videos', $videoData);
        $this->assertTrue($createResponse['success']);
        $this->assertArrayHasKey('video_id', $createResponse['data']);

        $videoId = $createResponse['data']['video_id'];

        // 4. 获取视频详情
        $detailResponse = $this->apiRequest('GET', "/api/v1/videos/{$videoId}");
        $this->assertTrue($detailResponse['success']);
        $this->assertEquals($videoData['title'], $detailResponse['data']['title']);

        // 5. 更新视频信息
        $updateData = [
            'title' => '更新后的视频标题',
            'status' => 'published'
        ];

        $updateResponse = $this->apiRequest('PUT', "/api/v1/videos/{$videoId}", $updateData);
        $this->assertTrue($updateResponse['success']);

        // 6. 验证更新结果
        $updatedDetailResponse = $this->apiRequest('GET', "/api/v1/videos/{$videoId}");
        $this->assertEquals($updateData['title'], $updatedDetailResponse['data']['title']);
        $this->assertEquals($updateData['status'], $updatedDetailResponse['data']['status']);
    }

    /**
     * 测试视频列表和搜索功能
     */
    public function testVideoListAndSearch(): void
    {
        // 创建多个测试视频
        $testVideos = [
            [
                'title' => '短视频测试1',
                'video_type' => 'short',
                'status' => 'published'
            ],
            [
                'title' => '长视频测试2',
                'video_type' => 'long',
                'status' => 'published'
            ],
            [
                'title' => '直播测试3',
                'video_type' => 'live',
                'status' => 'published'
            ]
        ];

        $createdVideoIds = [];
        foreach ($testVideos as $videoData) {
            $response = $this->apiRequest('POST', '/api/v1/videos', $videoData);
            $this->assertTrue($response['success']);
            $createdVideoIds[] = $response['data']['video_id'];
        }

        // 1. 测试获取所有视频列表
        $listResponse = $this->apiRequest('GET', '/api/v1/videos?page=1&limit=10');
        $this->assertTrue($listResponse['success']);
        $this->assertArrayHasKey('data', $listResponse['data']);
        $this->assertGreaterThanOrEqual(3, count($listResponse['data']['data']));

        // 2. 测试按类型筛选
        $shortVideoResponse = $this->apiRequest('GET', '/api/v1/videos?video_type=short');
        $this->assertTrue($shortVideoResponse['success']);
        
        foreach ($shortVideoResponse['data']['data'] as $video) {
            $this->assertEquals('short', $video['video_type']);
        }

        // 3. 测试搜索功能
        $searchResponse = $this->apiRequest('GET', '/api/v1/search/videos?keyword=测试');
        $this->assertTrue($searchResponse['success']);
        $this->assertGreaterThan(0, count($searchResponse['data']['data']));

        // 4. 测试分页功能
        $page1Response = $this->apiRequest('GET', '/api/v1/videos?page=1&limit=2');
        $page2Response = $this->apiRequest('GET', '/api/v1/videos?page=2&limit=2');
        
        $this->assertTrue($page1Response['success']);
        $this->assertTrue($page2Response['success']);
        $this->assertLessThanOrEqual(2, count($page1Response['data']['data']));
    }

    /**
     * 测试视频互动功能
     */
    public function testVideoInteractions(): void
    {
        // 创建测试视频
        $videoData = [
            'title' => '互动测试视频',
            'video_type' => 'short',
            'status' => 'published'
        ];

        $createResponse = $this->apiRequest('POST', '/api/v1/videos', $videoData);
        $videoId = $createResponse['data']['video_id'];

        // 1. 测试点赞功能
        $likeResponse = $this->apiRequest('POST', "/api/v1/videos/{$videoId}/like");
        $this->assertTrue($likeResponse['success']);

        // 2. 测试取消点赞
        $unlikeResponse = $this->apiRequest('DELETE', "/api/v1/videos/{$videoId}/like");
        $this->assertTrue($unlikeResponse['success']);

        // 3. 测试收藏功能
        $collectResponse = $this->apiRequest('POST', "/api/v1/videos/{$videoId}/collect");
        $this->assertTrue($collectResponse['success']);

        // 4. 测试评论功能
        $commentData = [
            'content' => '这是一个测试评论',
            'parent_id' => 0
        ];

        $commentResponse = $this->apiRequest('POST', "/api/v1/videos/{$videoId}/comments", $commentData);
        $this->assertTrue($commentResponse['success']);

        // 5. 测试获取评论列表
        $commentsResponse = $this->apiRequest('GET', "/api/v1/videos/{$videoId}/comments");
        $this->assertTrue($commentsResponse['success']);
        $this->assertGreaterThan(0, count($commentsResponse['data']['data']));

        // 6. 测试播放统计
        $playResponse = $this->apiRequest('POST', "/api/v1/videos/{$videoId}/play");
        $this->assertTrue($playResponse['success']);

        // 验证播放次数增加
        $detailResponse = $this->apiRequest('GET', "/api/v1/videos/{$videoId}");
        $this->assertGreaterThan(0, $detailResponse['data']['view_count']);
    }

    /**
     * 测试权限控制
     */
    public function testPermissionControl(): void
    {
        // 1. 测试未认证用户访问受保护的接口
        $this->testToken = null; // 移除认证令牌

        $unauthorizedResponse = $this->apiRequest('POST', '/api/v1/videos', [
            'title' => '未授权测试'
        ]);

        $this->assertFalse($unauthorizedResponse['success']);
        $this->assertContains('unauthorized', strtolower($unauthorizedResponse['message']));

        // 2. 重新设置令牌
        $userData = [
            'id' => $this->testUser->id,
            'username' => $this->testUser->username,
            'email' => $this->testUser->email,
            'level' => $this->testUser->level
        ];
        $this->testToken = $this->jwtService->generateAccessToken($userData);

        // 3. 测试用户只能操作自己的视频
        $videoData = [
            'title' => '权限测试视频',
            'video_type' => 'short'
        ];

        $createResponse = $this->apiRequest('POST', '/api/v1/videos', $videoData);
        $videoId = $createResponse['data']['video_id'];

        // 创建另一个用户的令牌
        $otherUser = User::create([
            'username' => 'otheruser_' . time(),
            'email' => 'other_' . time() . '@example.com',
            'password' => password_hash('password123', PASSWORD_ARGON2ID),
            'status' => 'active',
            'level' => 1
        ]);

        $otherUserData = [
            'id' => $otherUser->id,
            'username' => $otherUser->username,
            'email' => $otherUser->email,
            'level' => $otherUser->level
        ];
        $otherToken = $this->jwtService->generateAccessToken($otherUserData);

        // 使用其他用户的令牌尝试修改视频
        $this->testToken = $otherToken;
        $updateResponse = $this->apiRequest('PUT', "/api/v1/videos/{$videoId}", [
            'title' => '尝试修改他人视频'
        ]);

        $this->assertFalse($updateResponse['success']);

        // 清理其他用户
        $otherUser->delete();
    }
}
