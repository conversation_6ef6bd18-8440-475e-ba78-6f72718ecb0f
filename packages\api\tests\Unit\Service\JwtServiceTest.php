<?php

namespace tests\Unit\Service;

use PHPUnit\Framework\TestCase;
use app\service\JwtService;
use Mockery;

/**
 * JWT服务单元测试
 * 
 * @covers \app\service\JwtService
 */
class JwtServiceTest extends TestCase
{
    private JwtService $jwtService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->jwtService = new JwtService();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 测试生成访问令牌
     * 
     * @covers \app\service\JwtService::generateAccessToken
     */
    public function testGenerateAccessToken(): void
    {
        $userData = [
            'id' => 1,
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'level' => 1
        ];
        $deviceId = 'test-device-123';

        $token = $this->jwtService->generateAccessToken($userData, $deviceId);

        $this->assertIsString($token);
        $this->assertNotEmpty($token);
        
        // 验证JWT格式 (header.payload.signature)
        $parts = explode('.', $token);
        $this->assertCount(3, $parts);
    }

    /**
     * 测试生成刷新令牌
     * 
     * @covers \app\service\JwtService::generateRefreshToken
     */
    public function testGenerateRefreshToken(): void
    {
        $userData = [
            'id' => 1,
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'level' => 1
        ];
        $deviceId = 'test-device-123';

        $token = $this->jwtService->generateRefreshToken($userData, $deviceId);

        $this->assertIsString($token);
        $this->assertNotEmpty($token);
        
        // 验证JWT格式
        $parts = explode('.', $token);
        $this->assertCount(3, $parts);
    }

    /**
     * 测试验证令牌
     * 
     * @covers \app\service\JwtService::validateToken
     */
    public function testValidateToken(): void
    {
        $userData = [
            'id' => 1,
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'level' => 1
        ];
        $deviceId = 'test-device-123';

        // 生成令牌
        $token = $this->jwtService->generateAccessToken($userData, $deviceId);

        // 验证令牌
        $result = $this->jwtService->validateToken($token);

        $this->assertTrue($result['valid']);
        $this->assertEquals($userData['id'], $result['data']['id']);
        $this->assertEquals($userData['username'], $result['data']['username']);
        $this->assertEquals($deviceId, $result['data']['device_id']);
    }

    /**
     * 测试验证无效令牌
     * 
     * @covers \app\service\JwtService::validateToken
     */
    public function testValidateInvalidToken(): void
    {
        $invalidToken = 'invalid.token.here';

        $result = $this->jwtService->validateToken($invalidToken);

        $this->assertFalse($result['valid']);
        $this->assertArrayHasKey('error', $result);
    }

    /**
     * 测试解析令牌数据
     * 
     * @covers \app\service\JwtService::parseToken
     */
    public function testParseToken(): void
    {
        $userData = [
            'id' => 1,
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'level' => 1
        ];
        $deviceId = 'test-device-123';

        $token = $this->jwtService->generateAccessToken($userData, $deviceId);
        $parsedData = $this->jwtService->parseToken($token);

        $this->assertIsArray($parsedData);
        $this->assertEquals($userData['id'], $parsedData['id']);
        $this->assertEquals($userData['username'], $parsedData['username']);
        $this->assertEquals($deviceId, $parsedData['device_id']);
    }

    /**
     * 测试令牌过期检查
     * 
     * @covers \app\service\JwtService::isTokenExpired
     */
    public function testIsTokenExpired(): void
    {
        $userData = [
            'id' => 1,
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'level' => 1
        ];

        // 生成一个新令牌（应该未过期）
        $token = $this->jwtService->generateAccessToken($userData);
        $isExpired = $this->jwtService->isTokenExpired($token);

        $this->assertFalse($isExpired);
    }

    /**
     * 测试撤销令牌
     * 
     * @covers \app\service\JwtService::revokeToken
     */
    public function testRevokeToken(): void
    {
        $userData = [
            'id' => 1,
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'level' => 1
        ];
        $deviceId = 'test-device-123';

        $token = $this->jwtService->generateAccessToken($userData, $deviceId);
        
        // 撤销令牌
        $result = $this->jwtService->revokeToken($token);
        $this->assertTrue($result);

        // 验证撤销后的令牌应该无效
        $validation = $this->jwtService->validateToken($token);
        $this->assertFalse($validation['valid']);
    }

    /**
     * 测试批量撤销用户令牌
     * 
     * @covers \app\service\JwtService::revokeUserTokens
     */
    public function testRevokeUserTokens(): void
    {
        $userId = 1;
        $deviceId = 'test-device-123';

        $result = $this->jwtService->revokeUserTokens($userId, $deviceId);
        $this->assertTrue($result);
    }

    /**
     * 测试获取令牌剩余时间
     * 
     * @covers \app\service\JwtService::getTokenTTL
     */
    public function testGetTokenTTL(): void
    {
        $userData = [
            'id' => 1,
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'level' => 1
        ];

        $token = $this->jwtService->generateAccessToken($userData);
        $ttl = $this->jwtService->getTokenTTL($token);

        $this->assertIsInt($ttl);
        $this->assertGreaterThan(0, $ttl);
    }
}
