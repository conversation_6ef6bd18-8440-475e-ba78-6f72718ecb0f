<?php

namespace app\service;

use think\facade\Log;
use think\facade\Cache;
use think\facade\Db;
use Elasticsearch\ClientBuilder;

/**
 * 性能监控服务
 * 
 * 功能：
 * 1. 应用性能监控
 * 2. 数据库性能监控
 * 3. 缓存性能监控
 * 4. API响应时间监控
 * 5. 错误率监控
 * 6. 系统资源监控
 */
class MonitoringService
{
    private $elasticsearch;
    private $startTime;
    private $metrics = [];

    public function __construct()
    {
        $this->startTime = microtime(true);
        
        // 初始化Elasticsearch客户端
        if (config('monitoring.elasticsearch.enabled')) {
            $this->elasticsearch = ClientBuilder::create()
                ->setHosts(config('monitoring.elasticsearch.hosts'))
                ->build();
        }
    }

    /**
     * 开始监控请求
     * 
     * @param string $endpoint API端点
     * @param string $method HTTP方法
     * @param array $params 请求参数
     */
    public function startRequest(string $endpoint, string $method, array $params = []): void
    {
        $this->metrics = [
            'request_id' => uniqid('req_'),
            'endpoint' => $endpoint,
            'method' => $method,
            'start_time' => microtime(true),
            'memory_start' => memory_get_usage(true),
            'params' => $this->sanitizeParams($params),
            'user_id' => request()->userInfo['id'] ?? null,
            'ip_address' => request()->ip(),
            'user_agent' => request()->header('User-Agent'),
            'db_queries' => [],
            'cache_operations' => [],
            'errors' => []
        ];

        // 记录请求开始日志
        Log::info('Request started', [
            'request_id' => $this->metrics['request_id'],
            'endpoint' => $endpoint,
            'method' => $method,
            'ip' => $this->metrics['ip_address']
        ]);
    }

    /**
     * 结束监控请求
     * 
     * @param int $statusCode HTTP状态码
     * @param mixed $response 响应数据
     */
    public function endRequest(int $statusCode, $response = null): void
    {
        $endTime = microtime(true);
        $this->metrics['end_time'] = $endTime;
        $this->metrics['duration'] = ($endTime - $this->metrics['start_time']) * 1000; // 毫秒
        $this->metrics['memory_end'] = memory_get_usage(true);
        $this->metrics['memory_peak'] = memory_get_peak_usage(true);
        $this->metrics['memory_used'] = $this->metrics['memory_end'] - $this->metrics['memory_start'];
        $this->metrics['status_code'] = $statusCode;
        $this->metrics['response_size'] = $response ? strlen(json_encode($response)) : 0;

        // 收集系统指标
        $this->collectSystemMetrics();

        // 记录性能指标
        $this->recordMetrics();

        // 检查性能阈值
        $this->checkPerformanceThresholds();

        // 发送到监控系统
        $this->sendToMonitoring();

        // 记录请求结束日志
        Log::info('Request completed', [
            'request_id' => $this->metrics['request_id'],
            'duration' => $this->metrics['duration'],
            'status_code' => $statusCode,
            'memory_used' => $this->formatBytes($this->metrics['memory_used'])
        ]);
    }

    /**
     * 记录数据库查询
     * 
     * @param string $sql SQL语句
     * @param array $bindings 绑定参数
     * @param float $duration 执行时间
     */
    public function recordDbQuery(string $sql, array $bindings, float $duration): void
    {
        $this->metrics['db_queries'][] = [
            'sql' => $sql,
            'bindings' => $bindings,
            'duration' => $duration * 1000, // 转换为毫秒
            'timestamp' => microtime(true)
        ];

        // 记录慢查询
        if ($duration > config('monitoring.slow_query_threshold', 1.0)) {
            Log::warning('Slow database query detected', [
                'request_id' => $this->metrics['request_id'] ?? null,
                'sql' => $sql,
                'duration' => $duration,
                'bindings' => $bindings
            ]);
        }
    }

    /**
     * 记录缓存操作
     * 
     * @param string $operation 操作类型 (get/set/delete)
     * @param string $key 缓存键
     * @param bool $hit 是否命中
     * @param float $duration 执行时间
     */
    public function recordCacheOperation(string $operation, string $key, bool $hit, float $duration): void
    {
        $this->metrics['cache_operations'][] = [
            'operation' => $operation,
            'key' => $key,
            'hit' => $hit,
            'duration' => $duration * 1000,
            'timestamp' => microtime(true)
        ];
    }

    /**
     * 记录错误
     * 
     * @param \Throwable $exception 异常对象
     */
    public function recordError(\Throwable $exception): void
    {
        $error = [
            'type' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'timestamp' => microtime(true)
        ];

        $this->metrics['errors'][] = $error;

        // 记录错误日志
        Log::error('Application error', [
            'request_id' => $this->metrics['request_id'] ?? null,
            'error' => $error
        ]);

        // 发送错误告警
        $this->sendErrorAlert($error);
    }

    /**
     * 收集系统指标
     */
    private function collectSystemMetrics(): void
    {
        // CPU使用率
        $load = sys_getloadavg();
        $this->metrics['system'] = [
            'load_1min' => $load[0],
            'load_5min' => $load[1],
            'load_15min' => $load[2],
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'timestamp' => time()
        ];

        // 数据库连接数
        try {
            $dbConnections = Db::query("SHOW STATUS LIKE 'Threads_connected'");
            $this->metrics['system']['db_connections'] = $dbConnections[0]['Value'] ?? 0;
        } catch (\Exception $e) {
            // 忽略数据库连接错误
        }

        // Redis连接数
        try {
            $redisInfo = Cache::store('redis')->handler()->info('clients');
            $this->metrics['system']['redis_connections'] = $redisInfo['connected_clients'] ?? 0;
        } catch (\Exception $e) {
            // 忽略Redis连接错误
        }
    }

    /**
     * 记录性能指标到本地存储
     */
    private function recordMetrics(): void
    {
        // 计算统计数据
        $stats = [
            'total_queries' => count($this->metrics['db_queries']),
            'total_query_time' => array_sum(array_column($this->metrics['db_queries'], 'duration')),
            'cache_hits' => count(array_filter($this->metrics['cache_operations'], fn($op) => $op['hit'])),
            'cache_misses' => count(array_filter($this->metrics['cache_operations'], fn($op) => !$op['hit'])),
            'error_count' => count($this->metrics['errors'])
        ];

        $this->metrics['stats'] = $stats;

        // 存储到缓存中用于实时监控
        $cacheKey = 'monitoring:metrics:' . date('Y-m-d-H-i');
        $existingMetrics = Cache::get($cacheKey, []);
        $existingMetrics[] = $this->metrics;
        Cache::set($cacheKey, $existingMetrics, 3600); // 保存1小时
    }

    /**
     * 检查性能阈值
     */
    private function checkPerformanceThresholds(): void
    {
        $thresholds = config('monitoring.thresholds');

        // 检查响应时间
        if ($this->metrics['duration'] > $thresholds['response_time']) {
            $this->sendAlert('slow_response', [
                'duration' => $this->metrics['duration'],
                'threshold' => $thresholds['response_time'],
                'endpoint' => $this->metrics['endpoint']
            ]);
        }

        // 检查内存使用
        if ($this->metrics['memory_used'] > $thresholds['memory_usage']) {
            $this->sendAlert('high_memory', [
                'memory_used' => $this->formatBytes($this->metrics['memory_used']),
                'threshold' => $this->formatBytes($thresholds['memory_usage']),
                'endpoint' => $this->metrics['endpoint']
            ]);
        }

        // 检查数据库查询数量
        if (count($this->metrics['db_queries']) > $thresholds['db_queries']) {
            $this->sendAlert('too_many_queries', [
                'query_count' => count($this->metrics['db_queries']),
                'threshold' => $thresholds['db_queries'],
                'endpoint' => $this->metrics['endpoint']
            ]);
        }
    }

    /**
     * 发送到监控系统
     */
    private function sendToMonitoring(): void
    {
        if (!$this->elasticsearch) {
            return;
        }

        try {
            // 发送到Elasticsearch
            $this->elasticsearch->index([
                'index' => 'app-metrics-' . date('Y.m.d'),
                'body' => array_merge($this->metrics, [
                    '@timestamp' => date('c'),
                    'application' => config('app.name'),
                    'environment' => config('app.env')
                ])
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send metrics to Elasticsearch', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 发送告警
     * 
     * @param string $type 告警类型
     * @param array $data 告警数据
     */
    private function sendAlert(string $type, array $data): void
    {
        $alert = [
            'type' => $type,
            'data' => $data,
            'request_id' => $this->metrics['request_id'] ?? null,
            'timestamp' => time(),
            'severity' => $this->getAlertSeverity($type)
        ];

        // 记录告警日志
        Log::warning('Performance alert triggered', $alert);

        // 这里可以集成钉钉、企业微信、邮件等告警通道
        // $this->sendToAlertChannel($alert);
    }

    /**
     * 发送错误告警
     * 
     * @param array $error 错误信息
     */
    private function sendErrorAlert(array $error): void
    {
        // 检查是否需要发送告警（避免重复告警）
        $errorHash = md5($error['type'] . $error['message'] . $error['file'] . $error['line']);
        $cacheKey = 'error_alert:' . $errorHash;
        
        if (Cache::has($cacheKey)) {
            return; // 1小时内相同错误不重复告警
        }
        
        Cache::set($cacheKey, true, 3600);

        $alert = [
            'type' => 'application_error',
            'error' => $error,
            'request_id' => $this->metrics['request_id'] ?? null,
            'timestamp' => time(),
            'severity' => 'high'
        ];

        Log::error('Error alert triggered', $alert);
    }

    /**
     * 获取告警严重程度
     * 
     * @param string $type 告警类型
     * @return string
     */
    private function getAlertSeverity(string $type): string
    {
        $severityMap = [
            'slow_response' => 'medium',
            'high_memory' => 'high',
            'too_many_queries' => 'medium',
            'application_error' => 'high'
        ];

        return $severityMap[$type] ?? 'low';
    }

    /**
     * 清理敏感参数
     * 
     * @param array $params 原始参数
     * @return array
     */
    private function sanitizeParams(array $params): array
    {
        $sensitiveKeys = ['password', 'token', 'secret', 'key', 'authorization'];
        
        foreach ($params as $key => $value) {
            if (in_array(strtolower($key), $sensitiveKeys)) {
                $params[$key] = '***';
            }
        }

        return $params;
    }

    /**
     * 格式化字节数
     * 
     * @param int $bytes 字节数
     * @return string
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen($bytes) - 1) / 3);
        
        return sprintf("%.2f %s", $bytes / pow(1024, $factor), $units[$factor]);
    }

    /**
     * 获取实时监控数据
     * 
     * @return array
     */
    public static function getRealTimeMetrics(): array
    {
        $currentMinute = date('Y-m-d-H-i');
        $metrics = Cache::get('monitoring:metrics:' . $currentMinute, []);
        
        if (empty($metrics)) {
            return [
                'requests' => 0,
                'avg_response_time' => 0,
                'error_rate' => 0,
                'db_queries' => 0,
                'cache_hit_rate' => 0
            ];
        }

        $totalRequests = count($metrics);
        $totalResponseTime = array_sum(array_column($metrics, 'duration'));
        $totalErrors = array_sum(array_column($metrics, 'stats.error_count'));
        $totalDbQueries = array_sum(array_column($metrics, 'stats.total_queries'));
        $totalCacheHits = array_sum(array_column($metrics, 'stats.cache_hits'));
        $totalCacheOperations = array_sum(array_map(function($m) {
            return $m['stats']['cache_hits'] + $m['stats']['cache_misses'];
        }, $metrics));

        return [
            'requests' => $totalRequests,
            'avg_response_time' => $totalRequests > 0 ? round($totalResponseTime / $totalRequests, 2) : 0,
            'error_rate' => $totalRequests > 0 ? round(($totalErrors / $totalRequests) * 100, 2) : 0,
            'db_queries' => $totalDbQueries,
            'cache_hit_rate' => $totalCacheOperations > 0 ? round(($totalCacheHits / $totalCacheOperations) * 100, 2) : 0
        ];
    }
}
