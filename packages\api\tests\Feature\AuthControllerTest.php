<?php

namespace tests\Feature;

use PHPUnit\Framework\TestCase;
use think\App;
use think\Request;
use app\controller\Auth;
use app\model\User;
use Mockery;

/**
 * 认证控制器功能测试
 * 
 * @covers \app\controller\Auth
 */
class AuthControllerTest extends TestCase
{
    private Auth $authController;
    private $mockRequest;
    private $mockApp;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockApp = Mockery::mock(App::class);
        $this->mockRequest = Mockery::mock(Request::class);
        
        $this->authController = new Auth($this->mockApp);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 测试用户登录成功
     * 
     * @covers \app\controller\Auth::login
     */
    public function testLoginSuccess(): void
    {
        $loginData = [
            'username' => 'testuser',
            'password' => 'password123'
        ];

        // Mock请求数据
        $this->mockRequest
            ->shouldReceive('post')
            ->once()
            ->andReturn($loginData);

        // Mock用户模型
        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('isActive')
            ->once()
            ->andReturn(true);
        
        $mockUser->shouldReceive('getBasicInfo')
            ->once()
            ->andReturn([
                'id' => 1,
                'username' => 'testuser',
                'email' => '<EMAIL>',
                'level' => 1
            ]);

        $mockUser->id = 1;
        $mockUser->username = 'testuser';
        $mockUser->email = '<EMAIL>';
        $mockUser->level = 1;

        // Mock User::login方法
        User::shouldReceive('login')
            ->with($loginData['username'], $loginData['password'])
            ->once()
            ->andReturn($mockUser);

        // Mock请求头
        $this->mockRequest
            ->shouldReceive('header')
            ->with('X-Device-ID')
            ->once()
            ->andReturn('test-device-123');

        $response = $this->authController->login($this->mockRequest);
        $responseData = json_decode($response->getContent(), true);

        $this->assertTrue($responseData['success']);
        $this->assertEquals('登录成功', $responseData['message']);
        $this->assertArrayHasKey('access_token', $responseData['data']);
        $this->assertArrayHasKey('refresh_token', $responseData['data']);
    }

    /**
     * 测试用户登录失败 - 用户名或密码错误
     * 
     * @covers \app\controller\Auth::login
     */
    public function testLoginFailureInvalidCredentials(): void
    {
        $loginData = [
            'username' => 'wronguser',
            'password' => 'wrongpassword'
        ];

        $this->mockRequest
            ->shouldReceive('post')
            ->once()
            ->andReturn($loginData);

        // Mock User::login返回null（登录失败）
        User::shouldReceive('login')
            ->with($loginData['username'], $loginData['password'])
            ->once()
            ->andReturn(null);

        $response = $this->authController->login($this->mockRequest);
        $responseData = json_decode($response->getContent(), true);

        $this->assertFalse($responseData['success']);
        $this->assertEquals('用户名或密码错误', $responseData['message']);
    }

    /**
     * 测试用户登录失败 - 账户被禁用
     * 
     * @covers \app\controller\Auth::login
     */
    public function testLoginFailureAccountDisabled(): void
    {
        $loginData = [
            'username' => 'testuser',
            'password' => 'password123'
        ];

        $this->mockRequest
            ->shouldReceive('post')
            ->once()
            ->andReturn($loginData);

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('isActive')
            ->once()
            ->andReturn(false);

        User::shouldReceive('login')
            ->with($loginData['username'], $loginData['password'])
            ->once()
            ->andReturn($mockUser);

        $response = $this->authController->login($this->mockRequest);
        $responseData = json_decode($response->getContent(), true);

        $this->assertFalse($responseData['success']);
        $this->assertEquals('账户已被禁用', $responseData['message']);
    }

    /**
     * 测试用户注册成功
     * 
     * @covers \app\controller\Auth::register
     */
    public function testRegisterSuccess(): void
    {
        $registerData = [
            'username' => 'newuser',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'confirm_password' => 'password123'
        ];

        $this->mockRequest
            ->shouldReceive('post')
            ->once()
            ->andReturn($registerData);

        // Mock用户不存在检查
        User::shouldReceive('where')
            ->with('username', $registerData['username'])
            ->once()
            ->andReturnSelf();

        User::shouldReceive('whereOr')
            ->with('email', $registerData['email'])
            ->once()
            ->andReturnSelf();

        User::shouldReceive('find')
            ->once()
            ->andReturn(null);

        // Mock用户创建
        $mockNewUser = Mockery::mock();
        $mockNewUser->shouldReceive('save')
            ->once()
            ->andReturn(true);

        $mockNewUser->shouldReceive('getBasicInfo')
            ->once()
            ->andReturn([
                'id' => 2,
                'username' => 'newuser',
                'email' => '<EMAIL>',
                'level' => 1
            ]);

        $mockNewUser->id = 2;
        $mockNewUser->username = 'newuser';
        $mockNewUser->email = '<EMAIL>';
        $mockNewUser->level = 1;

        User::shouldReceive('create')
            ->once()
            ->andReturn($mockNewUser);

        $response = $this->authController->register($this->mockRequest);
        $responseData = json_decode($response->getContent(), true);

        $this->assertTrue($responseData['success']);
        $this->assertEquals('注册成功', $responseData['message']);
        $this->assertArrayHasKey('access_token', $responseData['data']);
    }

    /**
     * 测试用户注册失败 - 用户名已存在
     * 
     * @covers \app\controller\Auth::register
     */
    public function testRegisterFailureUsernameExists(): void
    {
        $registerData = [
            'username' => 'existinguser',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'confirm_password' => 'password123'
        ];

        $this->mockRequest
            ->shouldReceive('post')
            ->once()
            ->andReturn($registerData);

        // Mock用户已存在
        $existingUser = Mockery::mock();
        User::shouldReceive('where')
            ->with('username', $registerData['username'])
            ->once()
            ->andReturnSelf();

        User::shouldReceive('whereOr')
            ->with('email', $registerData['email'])
            ->once()
            ->andReturnSelf();

        User::shouldReceive('find')
            ->once()
            ->andReturn($existingUser);

        $response = $this->authController->register($this->mockRequest);
        $responseData = json_decode($response->getContent(), true);

        $this->assertFalse($responseData['success']);
        $this->assertEquals('用户名或邮箱已存在', $responseData['message']);
    }

    /**
     * 测试获取用户信息
     * 
     * @covers \app\controller\Auth::me
     */
    public function testGetUserInfo(): void
    {
        $userInfo = [
            'id' => 1,
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'level' => 1
        ];

        $this->mockRequest->userInfo = $userInfo;

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getDetailInfo')
            ->once()
            ->andReturn(array_merge($userInfo, [
                'avatar' => '/uploads/avatars/default.jpg',
                'created_at' => '2024-01-01 00:00:00'
            ]));

        User::shouldReceive('find')
            ->with($userInfo['id'])
            ->once()
            ->andReturn($mockUser);

        $response = $this->authController->me($this->mockRequest);
        $responseData = json_decode($response->getContent(), true);

        $this->assertTrue($responseData['success']);
        $this->assertEquals($userInfo['id'], $responseData['data']['id']);
        $this->assertEquals($userInfo['username'], $responseData['data']['username']);
    }

    /**
     * 测试用户登出
     * 
     * @covers \app\controller\Auth::logout
     */
    public function testLogout(): void
    {
        $userInfo = [
            'id' => 1,
            'username' => 'testuser'
        ];

        $this->mockRequest->userInfo = $userInfo;
        $this->mockRequest->token = 'test-token-123';

        $this->mockRequest
            ->shouldReceive('header')
            ->with('Authorization')
            ->once()
            ->andReturn('Bearer test-token-123');

        $response = $this->authController->logout($this->mockRequest);
        $responseData = json_decode($response->getContent(), true);

        $this->assertTrue($responseData['success']);
        $this->assertEquals('登出成功', $responseData['message']);
    }

    /**
     * 测试刷新令牌
     * 
     * @covers \app\controller\Auth::refresh
     */
    public function testRefreshToken(): void
    {
        $refreshData = [
            'refresh_token' => 'valid-refresh-token'
        ];

        $this->mockRequest
            ->shouldReceive('post')
            ->once()
            ->andReturn($refreshData);

        // 这里需要mock JwtService的相关方法
        // 由于涉及到复杂的JWT逻辑，实际测试中需要更详细的mock

        $response = $this->authController->refresh($this->mockRequest);
        $responseData = json_decode($response->getContent(), true);

        // 基本的响应结构检查
        $this->assertArrayHasKey('success', $responseData);
        $this->assertArrayHasKey('message', $responseData);
    }
}
