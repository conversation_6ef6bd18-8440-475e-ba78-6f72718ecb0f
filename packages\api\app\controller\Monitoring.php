<?php

namespace app\controller;

use think\Request;
use think\Response;
use app\service\MonitoringService;

/**
 * 监控控制器
 */
class Monitoring
{
    protected $monitoringService;

    public function __construct()
    {
        $this->monitoringService = new MonitoringService();
    }

    /**
     * 健康检查
     */
    public function health(Request $request): Response
    {
        try {
            $health = [
                'status' => 'healthy',
                'timestamp' => time(),
                'services' => [
                    'database' => $this->checkDatabase(),
                    'redis' => $this->checkRedis(),
                    'storage' => $this->checkStorage(),
                ]
            ];

            return json([
                'code' => 200,
                'message' => '系统健康',
                'data' => $health
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '健康检查失败',
                'data' => ['error' => $e->getMessage()]
            ]);
        }
    }

    /**
     * 实时监控数据
     */
    public function realtime(Request $request): Response
    {
        try {
            $data = [
                'timestamp' => time(),
                'system' => [
                    'memory_usage' => memory_get_usage(true),
                    'memory_peak' => memory_get_peak_usage(true),
                    'cpu_load' => sys_getloadavg(),
                ],
                'application' => [
                    'active_connections' => $this->getActiveConnections(),
                    'request_count' => $this->getRequestCount(),
                    'error_count' => $this->getErrorCount(),
                ],
                'database' => [
                    'connections' => $this->getDatabaseConnections(),
                    'slow_queries' => $this->getSlowQueries(),
                ],
                'cache' => [
                    'hit_rate' => $this->getCacheHitRate(),
                    'memory_usage' => $this->getCacheMemoryUsage(),
                ]
            ];

            return json([
                'code' => 200,
                'message' => '实时监控数据',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取监控数据失败',
                'data' => ['error' => $e->getMessage()]
            ]);
        }
    }

    /**
     * 性能指标
     */
    public function performance(Request $request): Response
    {
        try {
            $timeRange = $request->get('range', '1h'); // 1h, 6h, 24h, 7d
            
            $data = [
                'response_time' => $this->getResponseTimeMetrics($timeRange),
                'throughput' => $this->getThroughputMetrics($timeRange),
                'error_rate' => $this->getErrorRateMetrics($timeRange),
                'database_performance' => $this->getDatabasePerformanceMetrics($timeRange),
            ];

            return json([
                'code' => 200,
                'message' => '性能指标数据',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取性能指标失败',
                'data' => ['error' => $e->getMessage()]
            ]);
        }
    }

    /**
     * 错误监控
     */
    public function errors(Request $request): Response
    {
        try {
            $page = max(1, $request->get('page', 1));
            $limit = min($request->get('limit', 20), 100);
            $level = $request->get('level', 'all'); // error, warning, info, all

            $data = [
                'errors' => $this->getRecentErrors($page, $limit, $level),
                'summary' => $this->getErrorSummary(),
                'trends' => $this->getErrorTrends(),
            ];

            return json([
                'code' => 200,
                'message' => '错误监控数据',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取错误数据失败',
                'data' => ['error' => $e->getMessage()]
            ]);
        }
    }

    /**
     * 系统指标
     */
    public function metrics(Request $request): Response
    {
        try {
            $data = [
                'system' => [
                    'uptime' => $this->getSystemUptime(),
                    'memory' => $this->getMemoryMetrics(),
                    'disk' => $this->getDiskMetrics(),
                    'network' => $this->getNetworkMetrics(),
                ],
                'application' => [
                    'version' => config('app.version', '1.0.0'),
                    'environment' => config('app.env', 'production'),
                    'debug' => config('app.debug', false),
                ],
                'services' => [
                    'database' => $this->getDatabaseMetrics(),
                    'cache' => $this->getCacheMetrics(),
                    'queue' => $this->getQueueMetrics(),
                ]
            ];

            return json([
                'code' => 200,
                'message' => '系统指标数据',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取系统指标失败',
                'data' => ['error' => $e->getMessage()]
            ]);
        }
    }

    // 私有方法 - 健康检查相关

    private function checkDatabase(): array
    {
        try {
            \think\facade\Db::query('SELECT 1');
            return ['status' => 'healthy', 'message' => '数据库连接正常'];
        } catch (\Exception $e) {
            return ['status' => 'unhealthy', 'message' => '数据库连接失败: ' . $e->getMessage()];
        }
    }

    private function checkRedis(): array
    {
        try {
            \think\facade\Cache::set('health_check', time(), 10);
            return ['status' => 'healthy', 'message' => 'Redis连接正常'];
        } catch (\Exception $e) {
            return ['status' => 'unhealthy', 'message' => 'Redis连接失败: ' . $e->getMessage()];
        }
    }

    private function checkStorage(): array
    {
        try {
            $uploadPath = config('filesystem.disks.public.root', 'public/uploads');
            if (is_writable($uploadPath)) {
                return ['status' => 'healthy', 'message' => '存储可写'];
            } else {
                return ['status' => 'unhealthy', 'message' => '存储不可写'];
            }
        } catch (\Exception $e) {
            return ['status' => 'unhealthy', 'message' => '存储检查失败: ' . $e->getMessage()];
        }
    }

    // 私有方法 - 监控数据获取

    private function getActiveConnections(): int
    {
        // 模拟活跃连接数
        return rand(10, 100);
    }

    private function getRequestCount(): int
    {
        // 从缓存或日志中获取请求计数
        return \think\facade\Cache::get('request_count_today', 0);
    }

    private function getErrorCount(): int
    {
        // 从缓存或日志中获取错误计数
        return \think\facade\Cache::get('error_count_today', 0);
    }

    private function getDatabaseConnections(): int
    {
        try {
            $result = \think\facade\Db::query('SHOW STATUS LIKE "Threads_connected"');
            return $result[0]['Value'] ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getSlowQueries(): int
    {
        try {
            $result = \think\facade\Db::query('SHOW STATUS LIKE "Slow_queries"');
            return $result[0]['Value'] ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getCacheHitRate(): float
    {
        // 模拟缓存命中率
        return round(rand(85, 98) + rand(0, 99) / 100, 2);
    }

    private function getCacheMemoryUsage(): array
    {
        return [
            'used' => rand(100, 500) . 'MB',
            'total' => '1GB',
            'percentage' => rand(10, 50)
        ];
    }

    private function getResponseTimeMetrics(string $timeRange): array
    {
        // 模拟响应时间指标
        return [
            'avg' => rand(50, 200),
            'p50' => rand(40, 150),
            'p95' => rand(100, 300),
            'p99' => rand(200, 500),
            'unit' => 'ms'
        ];
    }

    private function getThroughputMetrics(string $timeRange): array
    {
        // 模拟吞吐量指标
        return [
            'requests_per_second' => rand(10, 100),
            'requests_per_minute' => rand(600, 6000),
            'total_requests' => rand(10000, 100000)
        ];
    }

    private function getErrorRateMetrics(string $timeRange): array
    {
        // 模拟错误率指标
        return [
            'error_rate' => round(rand(0, 5) + rand(0, 99) / 100, 2),
            'total_errors' => rand(0, 100),
            'total_requests' => rand(10000, 100000)
        ];
    }

    private function getDatabasePerformanceMetrics(string $timeRange): array
    {
        // 模拟数据库性能指标
        return [
            'avg_query_time' => rand(1, 50),
            'slow_queries' => rand(0, 10),
            'connections' => rand(5, 50),
            'unit' => 'ms'
        ];
    }

    private function getRecentErrors(int $page, int $limit, string $level): array
    {
        // 模拟错误数据
        $errors = [];
        for ($i = 0; $i < $limit; $i++) {
            $errors[] = [
                'id' => $i + 1,
                'level' => $level === 'all' ? ['error', 'warning', 'info'][rand(0, 2)] : $level,
                'message' => '示例错误消息 ' . ($i + 1),
                'file' => '/path/to/file.php',
                'line' => rand(1, 1000),
                'timestamp' => time() - rand(0, 3600),
                'count' => rand(1, 10)
            ];
        }
        return $errors;
    }

    private function getErrorSummary(): array
    {
        return [
            'total' => rand(0, 100),
            'error' => rand(0, 20),
            'warning' => rand(0, 50),
            'info' => rand(0, 30)
        ];
    }

    private function getErrorTrends(): array
    {
        // 模拟错误趋势数据
        $trends = [];
        for ($i = 23; $i >= 0; $i--) {
            $trends[] = [
                'hour' => date('H:i', time() - $i * 3600),
                'count' => rand(0, 10)
            ];
        }
        return $trends;
    }

    private function getSystemUptime(): string
    {
        return '5 days, 12 hours, 30 minutes';
    }

    private function getMemoryMetrics(): array
    {
        return [
            'used' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'limit' => ini_get('memory_limit')
        ];
    }

    private function getDiskMetrics(): array
    {
        $total = disk_total_space('.');
        $free = disk_free_space('.');
        $used = $total - $free;
        
        return [
            'total' => $total,
            'used' => $used,
            'free' => $free,
            'percentage' => round(($used / $total) * 100, 2)
        ];
    }

    private function getNetworkMetrics(): array
    {
        return [
            'bytes_sent' => rand(1000000, 10000000),
            'bytes_received' => rand(1000000, 10000000),
            'packets_sent' => rand(10000, 100000),
            'packets_received' => rand(10000, 100000)
        ];
    }

    private function getDatabaseMetrics(): array
    {
        return [
            'connections' => $this->getDatabaseConnections(),
            'slow_queries' => $this->getSlowQueries(),
            'uptime' => '5 days'
        ];
    }

    private function getCacheMetrics(): array
    {
        return [
            'hit_rate' => $this->getCacheHitRate(),
            'memory_usage' => $this->getCacheMemoryUsage(),
            'keys' => rand(1000, 10000)
        ];
    }

    private function getQueueMetrics(): array
    {
        return [
            'pending' => rand(0, 100),
            'processing' => rand(0, 10),
            'failed' => rand(0, 5),
            'completed' => rand(1000, 10000)
        ];
    }
}
